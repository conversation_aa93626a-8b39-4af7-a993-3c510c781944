import { useQuery, useMutation, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';
import { fetchData } from '../client';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  DateTime: { input: Date; output: Date; }
};

/** User/Employee allowances */
export type Allowance = {
  __typename?: 'Allowance';
  /** Amount of allowance */
  amount: Scalars['Float']['output'];
  /** Description of allowance */
  description?: Maybe<Scalars['String']['output']>;
  /** location allowance */
  location?: Maybe<Location>;
  /** allowance receipt */
  receipt?: Maybe<Scalars['String']['output']>;
  /** Status of allowance */
  status?: Maybe<AllowanceStatus>;
  /** Type of allowance */
  type: Scalars['String']['output'];
  /** allowance created by */
  user: User;
};

export type AllowanceClaim = {
  __typename?: 'AllowanceClaim';
  /** Claim amount */
  amount?: Maybe<Scalars['Float']['output']>;
  from: Scalars['DateTime']['output'];
  /** Purpose of the claim */
  purpose?: Maybe<Scalars['String']['output']>;
  receipts: Array<Scalars['String']['output']>;
  to: Scalars['DateTime']['output'];
  workingHours: Scalars['Int']['output'];
};

export enum AllowanceStatus {
  Approved = 'APPROVED',
  Pending = 'PENDING',
  Rejected = 'REJECTED'
}

export type AnalyticsFilterInput = {
  dateRange?: InputMaybe<DateRangeInput>;
  locationId?: InputMaybe<Scalars['String']['input']>;
  userRole?: InputMaybe<UserRoles>;
};

export type Anouncement = {
  __typename?: 'Anouncement';
  _id: Scalars['ID']['output'];
  createdAt: Scalars['DateTime']['output'];
  createdBy: User;
  date: Scalars['DateTime']['output'];
  description: Scalars['String']['output'];
  document?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  title: Scalars['String']['output'];
  updatedAt: Scalars['DateTime']['output'];
  userRoles?: Maybe<Array<UserRoles>>;
  users?: Maybe<Array<User>>;
};

/** User attendance */
export type Attendance = {
  __typename?: 'Attendance';
  _id: Scalars['ID']['output'];
  actualClockInTime: Scalars['DateTime']['output'];
  actualClockOutTime?: Maybe<Scalars['DateTime']['output']>;
  createdAt: Scalars['DateTime']['output'];
  date: Scalars['DateTime']['output'];
  endTime?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['ID']['output'];
  location?: Maybe<Location>;
  overTime?: Maybe<Scalars['DateTime']['output']>;
  overTimeSpentInMinutes: Scalars['Int']['output'];
  shift?: Maybe<Shift>;
  startTime: Scalars['DateTime']['output'];
  timeSpentInMinutes: Scalars['Int']['output'];
  updatedAt: Scalars['DateTime']['output'];
  user?: Maybe<User>;
};

export type AttendanceFilterInput = {
  dateRange?: InputMaybe<DateRangeInput>;
  locationIds?: InputMaybe<Array<Scalars['String']['input']>>;
  timeSpentLte?: InputMaybe<Scalars['Int']['input']>;
  userIds?: InputMaybe<Array<Scalars['String']['input']>>;
};

export type AttendanceInput = {
  shiftId: Scalars['String']['input'];
};

export type AttributeType = {
  __typename?: 'AttributeType';
  attibuteName: Scalars['String']['output'];
  attributeValues: Array<Scalars['String']['output']>;
};

export type AttributeTypeInput = {
  attibuteName: Scalars['String']['input'];
  attributeValues: Array<Scalars['String']['input']>;
};

export type AuthOutput = {
  __typename?: 'AuthOutput';
  /** Access token */
  access_token: Scalars['String']['output'];
};

export type Checkpoint = {
  __typename?: 'Checkpoint';
  _id: Scalars['ID']['output'];
  createdAt: Scalars['DateTime']['output'];
  deletedAt?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['ID']['output'];
  isDeleted: Scalars['Boolean']['output'];
  location: Location;
  locationCoordinates?: Maybe<Array<Scalars['Float']['output']>>;
  name: Scalars['String']['output'];
  updatedAt: Scalars['DateTime']['output'];
};

export type CheckpointAttendance = {
  __typename?: 'CheckpointAttendance';
  _id: Scalars['ID']['output'];
  checkpoint: Checkpoint;
  checkpointData: CheckpointData;
  createdAt: Scalars['DateTime']['output'];
  guard: User;
  id: Scalars['ID']['output'];
  location: Location;
  scannedAt: Scalars['DateTime']['output'];
  scannedLocation?: Maybe<Array<Scalars['Float']['output']>>;
  updatedAt: Scalars['DateTime']['output'];
};

export type CheckpointData = {
  __typename?: 'CheckpointData';
  locationCoordinates?: Maybe<Array<Scalars['Float']['output']>>;
  name: Scalars['String']['output'];
};

export type ClaimInput = {
  amount?: InputMaybe<Scalars['Float']['input']>;
  claimType: ClaimType;
  client?: InputMaybe<Scalars['String']['input']>;
  date?: InputMaybe<Scalars['DateTime']['input']>;
  distance?: InputMaybe<Scalars['Int']['input']>;
  from?: InputMaybe<Scalars['DateTime']['input']>;
  items?: InputMaybe<Array<Scalars['String']['input']>>;
  purpose?: InputMaybe<Scalars['String']['input']>;
  receipts?: InputMaybe<Array<Scalars['String']['input']>>;
  site?: InputMaybe<Scalars['String']['input']>;
  to?: InputMaybe<Scalars['DateTime']['input']>;
  toll?: InputMaybe<Scalars['String']['input']>;
  user: Scalars['String']['input'];
  workingHours?: InputMaybe<Scalars['Int']['input']>;
};

export enum ClaimStatus {
  Approved = 'APPROVED',
  Pending = 'PENDING',
  Rejected = 'REJECTED'
}

export enum ClaimType {
  Allowance = 'ALLOWANCE',
  Expense = 'EXPENSE',
  Site = 'SITE',
  Travel = 'TRAVEL'
}

export type ClaimUnion = AllowanceClaim | ExpenseClaim | SiteClaim | TravelClaim;

export type Claims = {
  __typename?: 'Claims';
  _id: Scalars['ID']['output'];
  /** claim data in calims */
  claimData: ClaimUnion;
  /** Type of the claim */
  claimType: ClaimType;
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  /** Claim processed by */
  processedBy?: Maybe<User>;
  rejectedReason?: Maybe<Scalars['String']['output']>;
  /** Claim status */
  status?: Maybe<ClaimStatus>;
  updatedAt: Scalars['DateTime']['output'];
  user: User;
};

export type ClearFaceInput = {
  userId: Scalars['String']['input'];
};

export type ClockInInput = {
  base64Img: Scalars['String']['input'];
  date: Scalars['DateTime']['input'];
  locationId: Scalars['String']['input'];
  shiftId: Scalars['String']['input'];
};

export type ClockOutInput = {
  attendanceId: Scalars['String']['input'];
};

export type Contact = {
  __typename?: 'Contact';
  countryCode: Scalars['String']['output'];
  phone: Scalars['String']['output'];
};

export type ContactInput = {
  countryCode: Scalars['String']['input'];
  phone: Scalars['String']['input'];
};

export type CreateAllowanceInput = {
  /** Example field (placeholder) */
  exampleField: Scalars['Int']['input'];
};

export type CreateAnouncementInput = {
  date: Scalars['DateTime']['input'];
  description: Scalars['String']['input'];
  document?: InputMaybe<Scalars['String']['input']>;
  title: Scalars['String']['input'];
  userRoles?: InputMaybe<Array<UserRoles>>;
  users?: InputMaybe<Array<Scalars['String']['input']>>;
};

export type CreateAttendanceInput = {
  date: Scalars['DateTime']['input'];
  endTime: Scalars['DateTime']['input'];
  locationId: Scalars['String']['input'];
  overTime: Scalars['DateTime']['input'];
  shiftId: Scalars['String']['input'];
  startTime: Scalars['DateTime']['input'];
  userId: Scalars['String']['input'];
};

export type CreateCheckpointAttendanceInput = {
  checkpointId: Scalars['String']['input'];
  locationId: Scalars['String']['input'];
  scannedLocation?: InputMaybe<Array<Scalars['Float']['input']>>;
};

export type CreateCheckpointInput = {
  location: Scalars['String']['input'];
  locationCoordinates?: InputMaybe<Array<Scalars['Float']['input']>>;
  name: Scalars['String']['input'];
};

export type CreateFolderDto = {
  folder: Scalars['String']['input'];
  parentFolderId?: InputMaybe<Scalars['String']['input']>;
  path: Scalars['String']['input'];
};

export type CreateHolidayInput = {
  /** date of the holiday */
  date: Scalars['DateTime']['input'];
  /** description of the holiday */
  description: Scalars['String']['input'];
  /** name of the holiday */
  name: Scalars['String']['input'];
};

export type CreateIncidentInput = {
  description: Scalars['String']['input'];
  evidence: Array<EvidenceInput>;
  priorityLevel: PriorityLevel;
};

export type CreateInventoryInput = {
  attributes?: InputMaybe<Array<AttributeTypeInput>>;
  description?: InputMaybe<Scalars['String']['input']>;
  /** Inventory item name */
  item: Scalars['String']['input'];
  items?: InputMaybe<Array<InventoryItemInputType>>;
  type: InventoryType;
};

export type CreateInventoryRequestInput = {
  inventory: Scalars['String']['input'];
  items: Array<RequestedItemInput>;
  requestedBy: Scalars['String']['input'];
};

export type CreateLeaveInput = {
  endDateTime: Scalars['DateTime']['input'];
  leaveType: LeaveType;
  reason: Scalars['String']['input'];
  startDateTime: Scalars['DateTime']['input'];
  user: Scalars['String']['input'];
};

export type CreateLocationInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  emergencyContact?: InputMaybe<Scalars['String']['input']>;
  geofence: GeoFenceInput;
  name: Scalars['String']['input'];
};

export type CreatePaymentConfigInput = {
  /** Example field (placeholder) */
  exampleField: Scalars['Int']['input'];
};

export type CreatePaymentCorrectionInput = {
  /** Example field (placeholder) */
  exampleField: Scalars['Int']['input'];
};

export type CreateShiftInput = {
  endDateTime: Scalars['DateTime']['input'];
  isRecurring?: InputMaybe<Scalars['Boolean']['input']>;
  locationId: Scalars['ID']['input'];
  overTime?: InputMaybe<Scalars['DateTime']['input']>;
  recurringId?: InputMaybe<Scalars['String']['input']>;
  startDateTime: Scalars['DateTime']['input'];
  userIds: Array<Scalars['ID']['input']>;
};

export type CreateTaskInput = {
  /** Example field (placeholder) */
  exampleField: Scalars['Int']['input'];
};

export type CreateUserDocumentInput = {
  documentName: Scalars['String']['input'];
  url: Scalars['String']['input'];
  user: Scalars['String']['input'];
};

export type CreateUserInput = {
  /** Employee Id */
  employeeId: Scalars['String']['input'];
  /** user fullname */
  fullname: Scalars['String']['input'];
  /** user password */
  password: Scalars['String']['input'];
  /** user phone number */
  phone: Scalars['String']['input'];
  /** user role */
  role: UserRoles;
  /** user active status */
  userStatus: UserStatus;
};

export type CreateUserProfileInput = {
  ID?: InputMaybe<Scalars['String']['input']>;
  bankAccNumber?: InputMaybe<Scalars['String']['input']>;
  bankName?: InputMaybe<Scalars['String']['input']>;
  currentAddress?: InputMaybe<Scalars['String']['input']>;
  dob?: InputMaybe<Scalars['DateTime']['input']>;
  emergencyContact?: InputMaybe<Array<EmergencyContactInput>>;
  gender?: InputMaybe<Scalars['String']['input']>;
  ic?: InputMaybe<Scalars['String']['input']>;
  joinedAt?: InputMaybe<Scalars['DateTime']['input']>;
  maritalStatus?: InputMaybe<Scalars['String']['input']>;
  passport?: InputMaybe<Scalars['String']['input']>;
  passportExpiresAt?: InputMaybe<Scalars['DateTime']['input']>;
  permitExpiresAt?: InputMaybe<Scalars['DateTime']['input']>;
  permitNumber?: InputMaybe<Scalars['String']['input']>;
  placeOfBirth?: InputMaybe<Scalars['String']['input']>;
  user: Scalars['String']['input'];
};

export type DateRangeInput = {
  endDate?: InputMaybe<Scalars['DateTime']['input']>;
  startDate?: InputMaybe<Scalars['DateTime']['input']>;
};

export type EmergencyContact = {
  __typename?: 'EmergencyContact';
  contact: Contact;
  name: Scalars['String']['output'];
  relation: Scalars['String']['output'];
};

export type EmergencyContactInput = {
  contact: ContactInput;
  name: Scalars['String']['input'];
  relation: Scalars['String']['input'];
};

export type Evidence = {
  __typename?: 'Evidence';
  type: EvidenceType;
  url: Scalars['String']['output'];
};

export type EvidenceInput = {
  type: EvidenceType;
  url: Scalars['String']['input'];
};

export enum EvidenceType {
  Document = 'DOCUMENT',
  Image = 'IMAGE',
  Video = 'VIDEO'
}

export type ExpenseClaim = {
  __typename?: 'ExpenseClaim';
  /** Claim amount */
  amount?: Maybe<Scalars['Float']['output']>;
  date: Scalars['DateTime']['output'];
  /** List of items */
  items: Array<Scalars['String']['output']>;
  /** Purpose of the claim */
  purpose?: Maybe<Scalars['String']['output']>;
  receipts: Array<Scalars['String']['output']>;
};

export type FaceInformation = {
  __typename?: 'FaceInformation';
  faceId?: Maybe<Scalars['String']['output']>;
};

export type FaceInformationInput = {
  faceId?: InputMaybe<Scalars['String']['input']>;
};

export type FindCheckpointsInput = {
  location?: InputMaybe<Scalars['String']['input']>;
};

export type FindClaimsInput = {
  claimType?: InputMaybe<ClaimType>;
  from?: InputMaybe<Scalars['DateTime']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
  to?: InputMaybe<Scalars['DateTime']['input']>;
  user?: InputMaybe<Scalars['String']['input']>;
};

export type FindInventoryRequestsInput = {
  inventoryType?: InputMaybe<InventoryType>;
  requestedBy?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<RequestStatus>;
};

export type FindLeavesInput = {
  dateRange?: InputMaybe<DateRangeInput>;
  leaveStatus?: InputMaybe<Scalars['String']['input']>;
  leaveType?: InputMaybe<Scalars['String']['input']>;
  user?: InputMaybe<Scalars['String']['input']>;
};

export type GeoFence = {
  __typename?: 'GeoFence';
  center: Array<Scalars['Float']['output']>;
  coords: Array<Array<Scalars['Float']['output']>>;
};

export type GeoFenceInput = {
  center: Array<Scalars['Float']['input']>;
  coords: Array<Array<Scalars['Float']['input']>>;
};

export type GuardActivityStats = {
  __typename?: 'GuardActivityStats';
  averageTimeSpent: Scalars['Float']['output'];
  totalAttendance: Scalars['Int']['output'];
  totalCheckpoints: Scalars['Int']['output'];
  totalIncidents: Scalars['Int']['output'];
};

/** The health of the server */
export type Health = {
  __typename?: 'Health';
  status: Scalars['Boolean']['output'];
};

export type Holiday = {
  __typename?: 'Holiday';
  _id: Scalars['ID']['output'];
  createdAt: Scalars['DateTime']['output'];
  date: Scalars['DateTime']['output'];
  description: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  updatedAt: Scalars['DateTime']['output'];
};

export type Incident = {
  __typename?: 'Incident';
  _id: Scalars['ID']['output'];
  createdAt: Scalars['DateTime']['output'];
  description: Scalars['String']['output'];
  evidence: Array<Evidence>;
  id: Scalars['ID']['output'];
  priorityLevel: PriorityLevel;
  reportedAt: Scalars['DateTime']['output'];
  reportedBy: User;
  updatedAt: Scalars['DateTime']['output'];
};

export type IndexFaceInput = {
  base64Img: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};

export type Inventory = {
  __typename?: 'Inventory';
  _id: Scalars['ID']['output'];
  attributes?: Maybe<Array<AttributeType>>;
  createdAt: Scalars['DateTime']['output'];
  description?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  /** Inventory item name */
  item: Scalars['String']['output'];
  items?: Maybe<Array<InventoryItem>>;
  type: InventoryType;
  updatedAt: Scalars['DateTime']['output'];
};

export type InventoryInput = {
  inventoryType: InventoryType;
};

export type InventoryItem = {
  __typename?: 'InventoryItem';
  costPrice: Scalars['Float']['output'];
  item: Scalars['String']['output'];
  quantity: Scalars['Int']['output'];
  sellingPrice: Scalars['Float']['output'];
  sku: Scalars['String']['output'];
};

export type InventoryItemInputType = {
  costPrice: Scalars['Float']['input'];
  item: Scalars['String']['input'];
  quantity: Scalars['Int']['input'];
  sellingPrice: Scalars['Float']['input'];
  sku: Scalars['String']['input'];
};

export type InventoryRequest = {
  __typename?: 'InventoryRequest';
  _id: Scalars['ID']['output'];
  acceptedAt?: Maybe<Scalars['DateTime']['output']>;
  acceptedBy?: Maybe<User>;
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  inventory: Inventory;
  inventoryType: InventoryType;
  items: Array<RequestedItem>;
  requestedBy: User;
  status: RequestStatus;
  updatedAt: Scalars['DateTime']['output'];
};

export type InventoryStats = {
  __typename?: 'InventoryStats';
  requestsPending: Scalars['Int']['output'];
  totalItems: Scalars['Int']['output'];
  type: Scalars['String']['output'];
};

export enum InventoryType {
  Guard = 'GUARD',
  Location = 'LOCATION'
}

export type Leave = {
  __typename?: 'Leave';
  _id: Scalars['ID']['output'];
  approvedBy?: Maybe<User>;
  createdAt: Scalars['DateTime']['output'];
  endDateTime: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  leaveStatus?: Maybe<LeaveStatus>;
  leaveType: LeaveType;
  reason: Scalars['String']['output'];
  rejectedReason?: Maybe<Scalars['String']['output']>;
  startDateTime: Scalars['DateTime']['output'];
  updatedAt: Scalars['DateTime']['output'];
  user?: Maybe<User>;
};

export type LeaveStats = {
  __typename?: 'LeaveStats';
  approved: Scalars['Int']['output'];
  pending: Scalars['Int']['output'];
  rejected: Scalars['Int']['output'];
};

export enum LeaveStatus {
  Approved = 'APPROVED',
  Pending = 'PENDING',
  Rejected = 'REJECTED'
}

export enum LeaveType {
  Fullday = 'FULLDAY',
  Halfday = 'HALFDAY'
}

export type Location = {
  __typename?: 'Location';
  _id: Scalars['ID']['output'];
  address?: Maybe<Scalars['String']['output']>;
  checkpoints?: Maybe<Array<Checkpoint>>;
  createdAt: Scalars['DateTime']['output'];
  description?: Maybe<Scalars['String']['output']>;
  emergencyContact?: Maybe<Scalars['String']['output']>;
  geofence: GeoFence;
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  updatedAt: Scalars['DateTime']['output'];
};

export type Mutation = {
  __typename?: 'Mutation';
  clearFace: Scalars['Boolean']['output'];
  clockIn: Attendance;
  clockOut: Attendance;
  createAllowance: Allowance;
  createAnouncement: Anouncement;
  createAttendance: Attendance;
  createCheckpoint: Checkpoint;
  createCheckpointAttendance: CheckpointAttendance;
  createClaim: Claims;
  createFolder: Storage;
  createHoliday: Holiday;
  createIncident: Incident;
  createInventory: Inventory;
  createInventoryRequest: InventoryRequest;
  createLeave: Leave;
  createLocation: Location;
  createPaymentConfig: PaymentConfig;
  createPaymentCorrection: PaymentCorrection;
  createShift?: Maybe<Scalars['Boolean']['output']>;
  createSignedUploadUrl: SignedUploadUrl;
  createTask: Task;
  createUserDocument: UserDocument;
  createUserProfile: UserProfile;
  indexFace: User;
  removeAllowance: Allowance;
  removeAnouncement: Anouncement;
  removeCheckpoint: Checkpoint;
  removeHoliday: Holiday;
  removeLocation: Location;
  removePaymentConfig: PaymentConfig;
  removePaymentCorrection: PaymentCorrection;
  removeRecurringShifts?: Maybe<Scalars['Boolean']['output']>;
  removeShift?: Maybe<Scalars['Boolean']['output']>;
  removeTask: Task;
  removeUserDocument: UserDocument;
  removeUserProfile: UserProfile;
  scanFace: AuthOutput;
  signIn: AuthOutput;
  signUp: AuthOutput;
  updateAllowance: Allowance;
  updateAnouncement: Anouncement;
  updateAttendance: Attendance;
  updateCheckpoint: Checkpoint;
  updateClaim: Claims;
  updateHoliday: Holiday;
  updateIncident: Incident;
  updateInventory: Inventory;
  updateInventoryRequest: InventoryRequest;
  updateLeave: Leave;
  updateLocation: Location;
  updatePaymentConfig: PaymentConfig;
  updatePaymentCorrection: PaymentCorrection;
  updateShift: Shift;
  updateTask: Task;
  updateUser: User;
  updateUserDocument: UserDocument;
  updateUserProfile: UserProfile;
};


export type MutationClearFaceArgs = {
  clearFaceInput: ClearFaceInput;
};


export type MutationClockInArgs = {
  clockInInput: ClockInInput;
};


export type MutationClockOutArgs = {
  clockOutInput: ClockOutInput;
};


export type MutationCreateAllowanceArgs = {
  createAllowanceInput: CreateAllowanceInput;
};


export type MutationCreateAnouncementArgs = {
  createAnouncementInput: CreateAnouncementInput;
};


export type MutationCreateAttendanceArgs = {
  createAttendanceInput: CreateAttendanceInput;
};


export type MutationCreateCheckpointArgs = {
  createCheckpointInput: CreateCheckpointInput;
};


export type MutationCreateCheckpointAttendanceArgs = {
  createCheckpointAttendanceInput: CreateCheckpointAttendanceInput;
};


export type MutationCreateClaimArgs = {
  input: ClaimInput;
};


export type MutationCreateFolderArgs = {
  createFolderInput: CreateFolderDto;
};


export type MutationCreateHolidayArgs = {
  createHolidayInput: CreateHolidayInput;
};


export type MutationCreateIncidentArgs = {
  createIncidentInput: CreateIncidentInput;
};


export type MutationCreateInventoryArgs = {
  createInventoryInput: CreateInventoryInput;
};


export type MutationCreateInventoryRequestArgs = {
  input: CreateInventoryRequestInput;
};


export type MutationCreateLeaveArgs = {
  createLeaveInput: CreateLeaveInput;
};


export type MutationCreateLocationArgs = {
  createLocationInput: CreateLocationInput;
};


export type MutationCreatePaymentConfigArgs = {
  createPaymentConfigInput: CreatePaymentConfigInput;
};


export type MutationCreatePaymentCorrectionArgs = {
  createPaymentCorrectionInput: CreatePaymentCorrectionInput;
};


export type MutationCreateShiftArgs = {
  createShiftInput: CreateShiftInput;
};


export type MutationCreateSignedUploadUrlArgs = {
  input: SignedUploadUrlInput;
};


export type MutationCreateTaskArgs = {
  createTaskInput: CreateTaskInput;
};


export type MutationCreateUserDocumentArgs = {
  createUserDocumentInput: CreateUserDocumentInput;
};


export type MutationCreateUserProfileArgs = {
  createUserProfileInput: CreateUserProfileInput;
};


export type MutationIndexFaceArgs = {
  indexFaceInput: IndexFaceInput;
};


export type MutationRemoveAllowanceArgs = {
  id: Scalars['Int']['input'];
};


export type MutationRemoveAnouncementArgs = {
  id: Scalars['String']['input'];
};


export type MutationRemoveCheckpointArgs = {
  id: Scalars['String']['input'];
};


export type MutationRemoveHolidayArgs = {
  id: Scalars['String']['input'];
};


export type MutationRemoveLocationArgs = {
  id: Scalars['ID']['input'];
};


export type MutationRemovePaymentConfigArgs = {
  id: Scalars['Int']['input'];
};


export type MutationRemovePaymentCorrectionArgs = {
  id: Scalars['Int']['input'];
};


export type MutationRemoveRecurringShiftsArgs = {
  recurringId: Scalars['String']['input'];
  shiftId: Scalars['String']['input'];
};


export type MutationRemoveShiftArgs = {
  shiftId: Scalars['String']['input'];
};


export type MutationRemoveTaskArgs = {
  id: Scalars['Int']['input'];
};


export type MutationRemoveUserDocumentArgs = {
  id: Scalars['String']['input'];
};


export type MutationRemoveUserProfileArgs = {
  id: Scalars['String']['input'];
};


export type MutationScanFaceArgs = {
  input: ScanFaceInput;
};


export type MutationSignInArgs = {
  input: SignInInput;
};


export type MutationSignUpArgs = {
  input: CreateUserInput;
};


export type MutationUpdateAllowanceArgs = {
  updateAllowanceInput: UpdateAllowanceInput;
};


export type MutationUpdateAnouncementArgs = {
  id: Scalars['String']['input'];
  updateAnouncementInput: UpdateAnouncementInput;
};


export type MutationUpdateAttendanceArgs = {
  updateAttendanceInput: UpdateAttendanceInput;
};


export type MutationUpdateCheckpointArgs = {
  id: Scalars['String']['input'];
  updateCheckpointInput: UpdateCheckpointInput;
};


export type MutationUpdateClaimArgs = {
  id: Scalars['String']['input'];
  updateClaimInput: UpdateClaimInput;
};


export type MutationUpdateHolidayArgs = {
  id: Scalars['String']['input'];
  updateHolidayInput: UpdateHolidayInput;
};


export type MutationUpdateIncidentArgs = {
  id: Scalars['String']['input'];
  updateIncidentInput: UpdateIncidentInput;
};


export type MutationUpdateInventoryArgs = {
  id: Scalars['String']['input'];
  updateInventoryInput: UpdateInventoryInput;
};


export type MutationUpdateInventoryRequestArgs = {
  id: Scalars['String']['input'];
  input: UpdateInventoryRequestInput;
};


export type MutationUpdateLeaveArgs = {
  id: Scalars['String']['input'];
  updateLeaveInput: UpdateLeaveInput;
};


export type MutationUpdateLocationArgs = {
  id: Scalars['ID']['input'];
  updateLocationInput: UpdateLocationInput;
};


export type MutationUpdatePaymentConfigArgs = {
  updatePaymentConfigInput: UpdatePaymentConfigInput;
};


export type MutationUpdatePaymentCorrectionArgs = {
  updatePaymentCorrectionInput: UpdatePaymentCorrectionInput;
};


export type MutationUpdateShiftArgs = {
  shiftId: Scalars['String']['input'];
  updateShiftInput: UpdateShiftInput;
};


export type MutationUpdateTaskArgs = {
  updateTaskInput: UpdateTaskInput;
};


export type MutationUpdateUserArgs = {
  updateUserInput: UpdateUserInput;
};


export type MutationUpdateUserDocumentArgs = {
  id: Scalars['String']['input'];
  updateUserDocumentInput: UpdateUserDocumentInput;
};


export type MutationUpdateUserProfileArgs = {
  id: Scalars['String']['input'];
  updateUserProfileInput: UpdateUserProfileInput;
};

export type PaymentConfig = {
  __typename?: 'PaymentConfig';
  holiday: PaymentConfigOption;
  location: Location;
  roles: Array<UserRoles>;
  weekDay: PaymentConfigOption;
  weekOff: PaymentConfigOption;
};

export type PaymentConfigOption = {
  __typename?: 'PaymentConfigOption';
  fullTimeAmount: Scalars['Float']['output'];
  overTimeAmount: Scalars['Float']['output'];
  paymentType: PaymentType;
};

export type PaymentCorrection = {
  __typename?: 'PaymentCorrection';
  amount: Scalars['Float']['output'];
  correctedBy: User;
  date: Scalars['DateTime']['output'];
  reason: Scalars['String']['output'];
  user: User;
};

export enum PaymentType {
  Daily = 'DAILY',
  Hourly = 'HOURLY',
  Monthly = 'MONTHLY'
}

export type PresignedFields = {
  __typename?: 'PresignedFields';
  Policy: Scalars['String']['output'];
  acl: Scalars['String']['output'];
  algorithm: Scalars['String']['output'];
  bucket: Scalars['String']['output'];
  credential: Scalars['String']['output'];
  date: Scalars['String']['output'];
  key: Scalars['String']['output'];
  signature: Scalars['String']['output'];
};

export enum PriorityLevel {
  Critical = 'CRITICAL',
  High = 'HIGH',
  Low = 'LOW',
  Medium = 'MEDIUM'
}

export type Query = {
  __typename?: 'Query';
  allAttendances: Array<Attendance>;
  allowance: Allowance;
  allowances: Array<Allowance>;
  anouncement: Anouncement;
  anouncements: Array<Anouncement>;
  attendance: Array<Attendance>;
  checkpoint: Checkpoint;
  checkpointAttendance: CheckpointAttendance;
  checkpointAttendances: Array<CheckpointAttendance>;
  checkpoints: Array<Checkpoint>;
  claim: Claims;
  claims: Array<Claims>;
  getAttendanceById: Attendance;
  getCheckpointAttendances: Array<CheckpointAttendance>;
  getGuardAttendances: Array<CheckpointAttendance>;
  getLocationAttendances: Array<CheckpointAttendance>;
  getShiftsByLocation: Shift;
  getUserShifts: Array<Shift>;
  guardActivityStats: GuardActivityStats;
  guardAttendanceTrends: Array<TimeSeriesData>;
  health: Health;
  holiday: Holiday;
  holidays: Array<Holiday>;
  incident: Incident;
  incidents: Array<Incident>;
  inventory: Array<Inventory>;
  inventoryById?: Maybe<Inventory>;
  inventoryRequest: InventoryRequest;
  inventoryRequests: Array<InventoryRequest>;
  inventoryStats: Array<InventoryStats>;
  leave: Leave;
  leaveStats: LeaveStats;
  leaves: Array<Leave>;
  location: Location;
  locations: Array<Location>;
  /** Logged in  user */
  me: User;
  myAttendances: Array<Attendance>;
  paymentConfig: PaymentConfig;
  paymentCorrection: PaymentCorrection;
  paymentCorrections: Array<PaymentCorrection>;
  shift: Shift;
  shifts: Array<Shift>;
  storageFolders: Array<Storage>;
  storageItems: Array<Storage>;
  systemFiles: Array<Storage>;
  task: Task;
  tasks: Array<Task>;
  user: User;
  userDocument: UserDocument;
  userDocuments: Array<UserDocument>;
  userProfile: UserProfile;
  userProfiles: Array<UserProfile>;
  users: Array<User>;
};


export type QueryAllAttendancesArgs = {
  filter?: InputMaybe<AttendanceFilterInput>;
};


export type QueryAllowanceArgs = {
  id: Scalars['Int']['input'];
};


export type QueryAnouncementArgs = {
  id: Scalars['String']['input'];
};


export type QueryAttendanceArgs = {
  attendanceInput: AttendanceInput;
};


export type QueryCheckpointArgs = {
  id: Scalars['String']['input'];
};


export type QueryCheckpointAttendanceArgs = {
  id: Scalars['String']['input'];
};


export type QueryCheckpointsArgs = {
  filter?: InputMaybe<FindCheckpointsInput>;
};


export type QueryClaimArgs = {
  id: Scalars['String']['input'];
};


export type QueryClaimsArgs = {
  filter?: InputMaybe<FindClaimsInput>;
};


export type QueryGetAttendanceByIdArgs = {
  id: Scalars['String']['input'];
};


export type QueryGetCheckpointAttendancesArgs = {
  checkpointId: Scalars['String']['input'];
};


export type QueryGetGuardAttendancesArgs = {
  guardId: Scalars['String']['input'];
};


export type QueryGetLocationAttendancesArgs = {
  locationId: Scalars['String']['input'];
};


export type QueryGetShiftsByLocationArgs = {
  locationId: Scalars['String']['input'];
};


export type QueryGetUserShiftsArgs = {
  shiftsInput: ShiftsInput;
};


export type QueryGuardActivityStatsArgs = {
  filter?: InputMaybe<AnalyticsFilterInput>;
};


export type QueryGuardAttendanceTrendsArgs = {
  filter?: InputMaybe<AnalyticsFilterInput>;
};


export type QueryHolidayArgs = {
  id: Scalars['String']['input'];
};


export type QueryIncidentArgs = {
  id: Scalars['String']['input'];
};


export type QueryInventoryArgs = {
  inventoryInput?: InputMaybe<InventoryInput>;
};


export type QueryInventoryByIdArgs = {
  id: Scalars['String']['input'];
};


export type QueryInventoryRequestArgs = {
  id: Scalars['String']['input'];
};


export type QueryInventoryRequestsArgs = {
  filter?: InputMaybe<FindInventoryRequestsInput>;
};


export type QueryLeaveArgs = {
  id: Scalars['String']['input'];
};


export type QueryLeaveStatsArgs = {
  filter?: InputMaybe<AnalyticsFilterInput>;
};


export type QueryLeavesArgs = {
  filter?: InputMaybe<FindLeavesInput>;
};


export type QueryLocationArgs = {
  id: Scalars['String']['input'];
};


export type QueryMyAttendancesArgs = {
  filter?: InputMaybe<AttendanceFilterInput>;
};


export type QueryPaymentConfigArgs = {
  id: Scalars['Int']['input'];
};


export type QueryPaymentCorrectionArgs = {
  id: Scalars['Int']['input'];
};


export type QueryShiftArgs = {
  shiftId: Scalars['String']['input'];
};


export type QueryShiftsArgs = {
  shiftsInput: ShiftsInput;
};


export type QueryStorageFoldersArgs = {
  parentId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryStorageItemsArgs = {
  parentId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryTaskArgs = {
  id: Scalars['Int']['input'];
};


export type QueryUserArgs = {
  id: Scalars['String']['input'];
};


export type QueryUserDocumentArgs = {
  id: Scalars['String']['input'];
};


export type QueryUserDocumentsArgs = {
  filter?: InputMaybe<UserDocumentFilterInput>;
};


export type QueryUserProfileArgs = {
  id: Scalars['String']['input'];
};


export type QueryUsersArgs = {
  usersInput?: InputMaybe<UsersInput>;
};

export enum RequestStatus {
  Accepted = 'ACCEPTED',
  Pending = 'PENDING',
  Rejected = 'REJECTED'
}

export type RequestedItem = {
  __typename?: 'RequestedItem';
  costPrice: Scalars['Float']['output'];
  item: Scalars['String']['output'];
  quantity: Scalars['Int']['output'];
  selectedAttributes: Array<SelectedAttribute>;
  sellingPrice: Scalars['Float']['output'];
  sku: Scalars['String']['output'];
};

export type RequestedItemInput = {
  item: Scalars['String']['input'];
  quantity: Scalars['Int']['input'];
  selectedAttributes: Array<SelectedAttributeInput>;
  sku: Scalars['String']['input'];
};

export type ScanFaceInput = {
  /** Base64 encoded face image for authentication */
  base64Img: Scalars['String']['input'];
};

export type SelectedAttribute = {
  __typename?: 'SelectedAttribute';
  attributeName: Scalars['String']['output'];
  value: Scalars['String']['output'];
};

export type SelectedAttributeInput = {
  attributeName: Scalars['String']['input'];
  value: Scalars['String']['input'];
};

export type Shift = {
  __typename?: 'Shift';
  _id: Scalars['ID']['output'];
  createdAt: Scalars['DateTime']['output'];
  endDateTime: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  isRecurring?: Maybe<Scalars['Boolean']['output']>;
  location?: Maybe<Location>;
  overTime?: Maybe<Scalars['DateTime']['output']>;
  recurringId?: Maybe<Scalars['String']['output']>;
  startDateTime: Scalars['DateTime']['output'];
  updatedAt: Scalars['DateTime']['output'];
  users?: Maybe<Array<User>>;
};

export type ShiftsInput = {
  endDateTime: Scalars['DateTime']['input'];
  locationId?: InputMaybe<Scalars['ID']['input']>;
  startDateTime: Scalars['DateTime']['input'];
  userId?: InputMaybe<Scalars['ID']['input']>;
};

export type SignInInput = {
  /** user password */
  password: Scalars['String']['input'];
  /** user phone number */
  phone: Scalars['String']['input'];
};

export type SignedUploadUrl = {
  __typename?: 'SignedUploadUrl';
  fields: PresignedFields;
  url: Scalars['String']['output'];
};

export type SignedUploadUrlInput = {
  contentType: Scalars['String']['input'];
  expiresIn?: InputMaybe<Scalars['Float']['input']>;
  key: Scalars['String']['input'];
};

export type SiteClaim = {
  __typename?: 'SiteClaim';
  /** Claim amount */
  amount?: Maybe<Scalars['Float']['output']>;
  items: Array<Scalars['String']['output']>;
  /** Purpose of the claim */
  purpose?: Maybe<Scalars['String']['output']>;
  receipts: Array<Scalars['String']['output']>;
  site: Location;
};

export type Storage = {
  __typename?: 'Storage';
  _id: Scalars['ID']['output'];
  createdAt: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  metadata?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  parent?: Maybe<Storage>;
  path?: Maybe<Scalars['String']['output']>;
  size: Scalars['Float']['output'];
  source: StorageItemSource;
  type: StorageItemType;
  updatedAt: Scalars['DateTime']['output'];
};

export enum StorageItemSource {
  System = 'SYSTEM',
  User = 'USER'
}

export enum StorageItemType {
  File = 'FILE',
  Folder = 'FOLDER'
}

export type Task = {
  __typename?: 'Task';
  date: Scalars['DateTime']['output'];
  description: Scalars['String']['output'];
  isRecurring: Scalars['Boolean']['output'];
  location: Location;
  shift: Shift;
  shiftRecurringId?: Maybe<Scalars['String']['output']>;
  taskStatus: TaskStatus;
  title: Scalars['String']['output'];
  users: Array<User>;
};

export enum TaskStatus {
  Completed = 'COMPLETED',
  InProgress = 'IN_PROGRESS',
  Pending = 'PENDING'
}

export type TimeSeriesData = {
  __typename?: 'TimeSeriesData';
  count: Scalars['Int']['output'];
  date: Scalars['String']['output'];
};

export type TravelClaim = {
  __typename?: 'TravelClaim';
  /** Claim amount */
  amount?: Maybe<Scalars['Float']['output']>;
  client: Scalars['String']['output'];
  distance: Scalars['Int']['output'];
  from: Scalars['DateTime']['output'];
  /** Purpose of the claim */
  purpose?: Maybe<Scalars['String']['output']>;
  receipts: Array<Scalars['String']['output']>;
  to: Scalars['DateTime']['output'];
  toll: Scalars['String']['output'];
};

export type UpdateAllowanceInput = {
  /** Example field (placeholder) */
  exampleField?: InputMaybe<Scalars['Int']['input']>;
  id: Scalars['Int']['input'];
};

export type UpdateAnouncementInput = {
  date?: InputMaybe<Scalars['DateTime']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  document?: InputMaybe<Scalars['String']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
  userRoles?: InputMaybe<Array<UserRoles>>;
  users?: InputMaybe<Array<Scalars['String']['input']>>;
};

export type UpdateAttendanceInput = {
  date: Scalars['DateTime']['input'];
  endTime: Scalars['DateTime']['input'];
  id: Scalars['String']['input'];
  locationId: Scalars['String']['input'];
  overTime: Scalars['DateTime']['input'];
  shiftId: Scalars['String']['input'];
  startTime: Scalars['DateTime']['input'];
  userId: Scalars['String']['input'];
};

export type UpdateCheckpointInput = {
  locationCoordinates?: InputMaybe<Array<Scalars['Float']['input']>>;
  name: Scalars['String']['input'];
};

export type UpdateClaimInput = {
  amount?: InputMaybe<Scalars['Float']['input']>;
  claimType: ClaimType;
  client?: InputMaybe<Scalars['String']['input']>;
  date?: InputMaybe<Scalars['DateTime']['input']>;
  distance?: InputMaybe<Scalars['Int']['input']>;
  from?: InputMaybe<Scalars['DateTime']['input']>;
  items?: InputMaybe<Array<Scalars['String']['input']>>;
  /** User ID of the person who processed the claim */
  processedBy?: InputMaybe<Scalars['String']['input']>;
  purpose?: InputMaybe<Scalars['String']['input']>;
  receipts?: InputMaybe<Array<Scalars['String']['input']>>;
  /** Reason for rejection, if applicable */
  rejectedReason?: InputMaybe<Scalars['String']['input']>;
  site?: InputMaybe<Scalars['String']['input']>;
  /** Updated claim status */
  status?: InputMaybe<ClaimStatus>;
  to?: InputMaybe<Scalars['DateTime']['input']>;
  toll?: InputMaybe<Scalars['String']['input']>;
  user?: InputMaybe<Scalars['String']['input']>;
  workingHours?: InputMaybe<Scalars['Int']['input']>;
};

export type UpdateHolidayInput = {
  /** date of the holiday */
  date?: InputMaybe<Scalars['DateTime']['input']>;
  /** description of the holiday */
  description?: InputMaybe<Scalars['String']['input']>;
  /** name of the holiday */
  name?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateIncidentInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  evidence?: InputMaybe<Array<EvidenceInput>>;
  priorityLevel?: InputMaybe<PriorityLevel>;
};

export type UpdateInventoryInput = {
  attributes?: InputMaybe<Array<AttributeTypeInput>>;
  description?: InputMaybe<Scalars['String']['input']>;
  /** Inventory item name */
  item?: InputMaybe<Scalars['String']['input']>;
  items?: InputMaybe<Array<InventoryItemInputType>>;
  type?: InputMaybe<InventoryType>;
};

export type UpdateInventoryRequestInput = {
  acceptedBy?: InputMaybe<Scalars['String']['input']>;
  status: RequestStatus;
};

export type UpdateLeaveInput = {
  approvedBy?: InputMaybe<Scalars['String']['input']>;
  leaveStatus: LeaveStatus;
  rejectedReason?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateLocationInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  description?: InputMaybe<Scalars['String']['input']>;
  emergencyContact?: InputMaybe<Scalars['String']['input']>;
  geofence?: InputMaybe<GeoFenceInput>;
  name?: InputMaybe<Scalars['String']['input']>;
};

export type UpdatePaymentConfigInput = {
  /** Example field (placeholder) */
  exampleField?: InputMaybe<Scalars['Int']['input']>;
  id: Scalars['Int']['input'];
};

export type UpdatePaymentCorrectionInput = {
  /** Example field (placeholder) */
  exampleField?: InputMaybe<Scalars['Int']['input']>;
  id: Scalars['Int']['input'];
};

export type UpdateShiftInput = {
  endDateTime?: InputMaybe<Scalars['DateTime']['input']>;
  fromDate?: InputMaybe<Scalars['DateTime']['input']>;
  isRecurring?: InputMaybe<Scalars['Boolean']['input']>;
  locationId?: InputMaybe<Scalars['ID']['input']>;
  overTime?: InputMaybe<Scalars['DateTime']['input']>;
  recurringId?: InputMaybe<Scalars['String']['input']>;
  startDateTime?: InputMaybe<Scalars['DateTime']['input']>;
  userIds?: InputMaybe<Array<Scalars['ID']['input']>>;
};

export type UpdateTaskInput = {
  /** Example field (placeholder) */
  exampleField?: InputMaybe<Scalars['Int']['input']>;
  id: Scalars['Int']['input'];
};

export type UpdateUserDocumentInput = {
  documentName?: InputMaybe<Scalars['String']['input']>;
  url?: InputMaybe<Scalars['String']['input']>;
  user?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateUserInput = {
  employeeId?: InputMaybe<Scalars['String']['input']>;
  faceInformation?: InputMaybe<FaceInformationInput>;
  /** user fullname */
  fullname?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  /** user password */
  password?: InputMaybe<Scalars['String']['input']>;
  /** user phone number */
  phone?: InputMaybe<Scalars['String']['input']>;
  profilePicture?: InputMaybe<Scalars['String']['input']>;
  /** user role */
  role?: InputMaybe<UserRoles>;
  /** user active status */
  userStatus?: InputMaybe<UserStatus>;
};

export type UpdateUserProfileInput = {
  ID?: InputMaybe<Scalars['String']['input']>;
  bankAccNumber?: InputMaybe<Scalars['String']['input']>;
  bankName?: InputMaybe<Scalars['String']['input']>;
  currentAddress?: InputMaybe<Scalars['String']['input']>;
  dob?: InputMaybe<Scalars['DateTime']['input']>;
  emergencyContact?: InputMaybe<Array<EmergencyContactInput>>;
  gender?: InputMaybe<Scalars['String']['input']>;
  ic?: InputMaybe<Scalars['String']['input']>;
  joinedAt?: InputMaybe<Scalars['DateTime']['input']>;
  maritalStatus?: InputMaybe<Scalars['String']['input']>;
  passport?: InputMaybe<Scalars['String']['input']>;
  passportExpiresAt?: InputMaybe<Scalars['DateTime']['input']>;
  permitExpiresAt?: InputMaybe<Scalars['DateTime']['input']>;
  permitNumber?: InputMaybe<Scalars['String']['input']>;
  placeOfBirth?: InputMaybe<Scalars['String']['input']>;
  user?: InputMaybe<Scalars['String']['input']>;
};

export type User = {
  __typename?: 'User';
  _id: Scalars['ID']['output'];
  createdAt: Scalars['DateTime']['output'];
  /** Employee Id */
  employeeId: Scalars['String']['output'];
  faceInformation?: Maybe<FaceInformation>;
  /** user fullname */
  fullname: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  /** user phone number */
  phone: Scalars['String']['output'];
  /** user profile picture URL from S3 */
  profilePicture?: Maybe<Scalars['String']['output']>;
  /** user role */
  role: UserRoles;
  updatedAt: Scalars['DateTime']['output'];
  /** user active status */
  userStatus: UserStatus;
};

export type UserDocument = {
  __typename?: 'UserDocument';
  _id: Scalars['ID']['output'];
  createdAt: Scalars['DateTime']['output'];
  documentName: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  updatedAt: Scalars['DateTime']['output'];
  url: Scalars['String']['output'];
  user: User;
};

export type UserDocumentFilterInput = {
  user?: InputMaybe<Scalars['String']['input']>;
};

export type UserProfile = {
  __typename?: 'UserProfile';
  ID?: Maybe<Scalars['String']['output']>;
  _id: Scalars['ID']['output'];
  bankAccNumber?: Maybe<Scalars['String']['output']>;
  bankName?: Maybe<Scalars['String']['output']>;
  createdAt: Scalars['DateTime']['output'];
  currentAddress?: Maybe<Scalars['String']['output']>;
  dob?: Maybe<Scalars['DateTime']['output']>;
  emergencyContact?: Maybe<Array<EmergencyContact>>;
  gender?: Maybe<Scalars['String']['output']>;
  ic?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  joinedAt?: Maybe<Scalars['DateTime']['output']>;
  maritalStatus?: Maybe<Scalars['String']['output']>;
  passport?: Maybe<Scalars['String']['output']>;
  passportExpiresAt?: Maybe<Scalars['DateTime']['output']>;
  permitExpiresAt?: Maybe<Scalars['DateTime']['output']>;
  permitNumber?: Maybe<Scalars['String']['output']>;
  placeOfBirth?: Maybe<Scalars['String']['output']>;
  updatedAt: Scalars['DateTime']['output'];
  user: Scalars['String']['output'];
};

export enum UserRoles {
  Admin = 'ADMIN',
  BufferGuard = 'BUFFER_GUARD',
  HrAdmin = 'HR_ADMIN',
  LocalGuard = 'LOCAL_GUARD',
  NepalGuard = 'NEPAL_GUARD',
  OperationsAdmin = 'OPERATIONS_ADMIN',
  OperationsManager = 'OPERATIONS_MANAGER'
}

export enum UserStatus {
  Active = 'ACTIVE',
  Inactive = 'INACTIVE'
}

export type UsersInput = {
  roles?: InputMaybe<Array<UserRoles>>;
};

export type AnnouncementsQueryVariables = Exact<{ [key: string]: never; }>;


export type AnnouncementsQuery = { __typename?: 'Query', anouncements: Array<{ __typename?: 'Anouncement', id: string, title: string, description: string, date: Date, userRoles?: Array<UserRoles> | null, document?: string | null, createdBy: { __typename?: 'User', id: string, fullname: string, role: UserRoles, profilePicture?: string | null } }> };

export type AnnouncementDetailQueryVariables = Exact<{
  id: Scalars['String']['input'];
}>;


export type AnnouncementDetailQuery = { __typename?: 'Query', anouncement: { __typename?: 'Anouncement', title: string, description: string, document?: string | null, createdAt: Date, _id: string, users?: Array<{ __typename?: 'User', id: string, createdAt: Date, updatedAt: Date, fullname: string, phone: string, userStatus: UserStatus, role: UserRoles }> | null, createdBy: { __typename?: 'User', id: string, fullname: string, role: UserRoles, profilePicture?: string | null } } };

export type AttendanceQueryVariables = Exact<{
  attendanceInput: AttendanceInput;
}>;


export type AttendanceQuery = { __typename?: 'Query', attendance: Array<{ __typename?: 'Attendance', id: string, createdAt: Date, updatedAt: Date, date: Date, endTime?: Date | null, overTime?: Date | null, overTimeSpentInMinutes: number, startTime: Date, timeSpentInMinutes: number, location?: { __typename?: 'Location', id: string, name: string } | null }> };

export type ClockInMutationVariables = Exact<{
  clockInInput: ClockInInput;
}>;


export type ClockInMutation = { __typename?: 'Mutation', clockIn: { __typename?: 'Attendance', id: string } };

export type ClockOutMutationVariables = Exact<{
  clockOutInput: ClockOutInput;
}>;


export type ClockOutMutation = { __typename?: 'Mutation', clockOut: { __typename?: 'Attendance', id: string } };

export type GetUserAttendanceQueryVariables = Exact<{
  filter?: InputMaybe<AttendanceFilterInput>;
}>;


export type GetUserAttendanceQuery = { __typename?: 'Query', myAttendances: Array<{ __typename?: 'Attendance', id: string, createdAt: Date, updatedAt: Date, date: Date, endTime?: Date | null, overTime?: Date | null, overTimeSpentInMinutes: number, startTime: Date, timeSpentInMinutes: number, actualClockInTime: Date, actualClockOutTime?: Date | null, shift?: { __typename?: 'Shift', startDateTime: Date, endDateTime: Date } | null, location?: { __typename?: 'Location', id: string, name: string } | null }> };

export type SigninMutationVariables = Exact<{
  input: SignInInput;
}>;


export type SigninMutation = { __typename?: 'Mutation', signIn: { __typename?: 'AuthOutput', access_token: string } };

export type FaceLoginMutationVariables = Exact<{
  base64Img: Scalars['String']['input'];
}>;


export type FaceLoginMutation = { __typename?: 'Mutation', scanFace: { __typename?: 'AuthOutput', access_token: string } };

export type ClaimsQueryVariables = Exact<{
  input?: InputMaybe<FindClaimsInput>;
}>;


export type ClaimsQuery = { __typename?: 'Query', claims: Array<{ __typename?: 'Claims', _id: string, id: string, claimType: ClaimType, status?: ClaimStatus | null, createdAt: Date, updatedAt: Date, rejectedReason?: string | null, user: { __typename?: 'User', _id: string, fullname: string, employeeId: string }, processedBy?: { __typename?: 'User', _id: string, fullname: string, employeeId: string } | null, claimData: { __typename?: 'AllowanceClaim', amount?: number | null, purpose?: string | null, from: Date, to: Date, workingHours: number, receipts: Array<string> } | { __typename?: 'ExpenseClaim', amount?: number | null, purpose?: string | null, items: Array<string>, date: Date, receipts: Array<string> } | { __typename?: 'SiteClaim', amount?: number | null, purpose?: string | null, items: Array<string>, receipts: Array<string>, site: { __typename?: 'Location', name: string } } | { __typename?: 'TravelClaim', amount?: number | null, purpose?: string | null, from: Date, to: Date, client: string, toll: string, distance: number, receipts: Array<string> } }> };

export type ClaimQueryVariables = Exact<{
  input: Scalars['String']['input'];
}>;


export type ClaimQuery = { __typename?: 'Query', claim: { __typename?: 'Claims', _id: string, id: string, createdAt: Date, updatedAt: Date, status?: ClaimStatus | null, claimType: ClaimType, rejectedReason?: string | null, claimData: { __typename?: 'AllowanceClaim', amount?: number | null, purpose?: string | null, from: Date, to: Date, workingHours: number, receipts: Array<string> } | { __typename?: 'ExpenseClaim', amount?: number | null, purpose?: string | null, items: Array<string>, date: Date, receipts: Array<string> } | { __typename?: 'SiteClaim', amount?: number | null, purpose?: string | null, items: Array<string>, receipts: Array<string> } | { __typename?: 'TravelClaim', amount?: number | null, purpose?: string | null, from: Date, to: Date, client: string, toll: string, distance: number, receipts: Array<string> }, user: { __typename?: 'User', _id: string, id: string, createdAt: Date, updatedAt: Date, fullname: string, phone: string, userStatus: UserStatus, role: UserRoles, employeeId: string, profilePicture?: string | null }, processedBy?: { __typename?: 'User', _id: string, id: string, createdAt: Date, updatedAt: Date, fullname: string, phone: string, userStatus: UserStatus, role: UserRoles, employeeId: string, profilePicture?: string | null } | null } };

export type CreateClaimMutationVariables = Exact<{
  input: ClaimInput;
}>;


export type CreateClaimMutation = { __typename?: 'Mutation', createClaim: { __typename?: 'Claims', _id: string, id: string, createdAt: Date, updatedAt: Date, status?: ClaimStatus | null, claimType: ClaimType, rejectedReason?: string | null } };

export type UpdateClaimMutationVariables = Exact<{
  id: Scalars['String']['input'];
  input: UpdateClaimInput;
}>;


export type UpdateClaimMutation = { __typename?: 'Mutation', updateClaim: { __typename?: 'Claims', _id: string } };

export type CreateSignedUploadUrlMutationVariables = Exact<{
  input: SignedUploadUrlInput;
}>;


export type CreateSignedUploadUrlMutation = { __typename?: 'Mutation', createSignedUploadUrl: { __typename?: 'SignedUploadUrl', url: string, fields: { __typename?: 'PresignedFields', key: string, bucket: string, acl: string, algorithm: string, credential: string, date: string, Policy: string, signature: string } } };

export type CreateUserDocumentMutationVariables = Exact<{
  input: CreateUserDocumentInput;
}>;


export type CreateUserDocumentMutation = { __typename?: 'Mutation', createUserDocument: { __typename?: 'UserDocument', id: string } };

export type UserDocumentsQueryVariables = Exact<{
  userDocumentFilterInput: UserDocumentFilterInput;
}>;


export type UserDocumentsQuery = { __typename?: 'Query', userDocuments: Array<{ __typename?: 'UserDocument', id: string, documentName: string, url: string, createdAt: Date }> };

export type CreateIncidentMutationVariables = Exact<{
  input: CreateIncidentInput;
}>;


export type CreateIncidentMutation = { __typename?: 'Mutation', createIncident: { __typename?: 'Incident', _id: string } };

export type CreateInventoryRequestMutationVariables = Exact<{
  input: CreateInventoryRequestInput;
}>;


export type CreateInventoryRequestMutation = { __typename?: 'Mutation', createInventoryRequest: { __typename?: 'InventoryRequest', _id: string, id: string, createdAt: Date, updatedAt: Date, status: RequestStatus, acceptedAt?: Date | null, inventoryType: InventoryType, inventory: { __typename?: 'Inventory', _id: string, id: string, createdAt: Date, updatedAt: Date, item: string, description?: string | null, type: InventoryType, attributes?: Array<{ __typename?: 'AttributeType', attibuteName: string, attributeValues: Array<string> }> | null, items?: Array<{ __typename?: 'InventoryItem', item: string, quantity: number, sku: string, costPrice: number, sellingPrice: number }> | null }, acceptedBy?: { __typename?: 'User', _id: string, id: string, createdAt: Date, updatedAt: Date, fullname: string, phone: string, userStatus: UserStatus, role: UserRoles, profilePicture?: string | null } | null, requestedBy: { __typename?: 'User', _id: string, id: string, createdAt: Date, updatedAt: Date, fullname: string, phone: string, userStatus: UserStatus, role: UserRoles, profilePicture?: string | null } } };

export type GetInventoryQueryVariables = Exact<{ [key: string]: never; }>;


export type GetInventoryQuery = { __typename?: 'Query', inventory: Array<{ __typename?: 'Inventory', _id: string, id: string, createdAt: Date, updatedAt: Date, item: string, description?: string | null, type: InventoryType, items?: Array<{ __typename?: 'InventoryItem', item: string, quantity: number, sku: string, costPrice: number, sellingPrice: number }> | null, attributes?: Array<{ __typename?: 'AttributeType', attibuteName: string, attributeValues: Array<string> }> | null }> };

export type CreateLeaveMutationVariables = Exact<{
  input: CreateLeaveInput;
}>;


export type CreateLeaveMutation = { __typename?: 'Mutation', createLeave: { __typename?: 'Leave', _id: string } };

export type GetLeavesQueryVariables = Exact<{
  input: FindLeavesInput;
}>;


export type GetLeavesQuery = { __typename?: 'Query', leaves: Array<{ __typename?: 'Leave', _id: string, id: string, createdAt: Date, updatedAt: Date, reason: string, leaveType: LeaveType, startDateTime: Date, endDateTime: Date, leaveStatus?: LeaveStatus | null, rejectedReason?: string | null, user?: { __typename?: 'User', _id: string, fullname: string, phone: string, userStatus: UserStatus, role: UserRoles } | null, approvedBy?: { __typename?: 'User', _id: string, fullname: string, phone: string, userStatus: UserStatus, role: UserRoles } | null }> };

export type LeaveQueryVariables = Exact<{
  id: Scalars['String']['input'];
}>;


export type LeaveQuery = { __typename?: 'Query', leave: { __typename?: 'Leave', _id: string, id: string, createdAt: Date, updatedAt: Date, reason: string, leaveType: LeaveType, startDateTime: Date, endDateTime: Date, leaveStatus?: LeaveStatus | null, rejectedReason?: string | null, approvedBy?: { __typename?: 'User', _id: string, id: string, createdAt: Date, updatedAt: Date, fullname: string, phone: string, userStatus: UserStatus, role: UserRoles, employeeId: string, profilePicture?: string | null } | null, user?: { __typename?: 'User', _id: string, id: string, createdAt: Date, updatedAt: Date, fullname: string, phone: string, userStatus: UserStatus, profilePicture?: string | null } | null } };

export type LocationsQueryVariables = Exact<{ [key: string]: never; }>;


export type LocationsQuery = { __typename?: 'Query', locations: Array<{ __typename?: 'Location', id: string, name: string, description?: string | null, address?: string | null, emergencyContact?: string | null }> };

export type MeQueryVariables = Exact<{ [key: string]: never; }>;


export type MeQuery = { __typename?: 'Query', me: { __typename?: 'User', _id: string, id: string, createdAt: Date, updatedAt: Date, fullname: string, phone: string, userStatus: UserStatus, role: UserRoles, profilePicture?: string | null, faceInformation?: { __typename?: 'FaceInformation', faceId?: string | null } | null } };

export type ShiftFragmentFragment = { __typename?: 'Shift', id: string, recurringId?: string | null, startDateTime: Date, endDateTime: Date, location?: { __typename?: 'Location', name: string, id: string, emergencyContact?: string | null, address?: string | null } | null, users?: Array<{ __typename?: 'User', fullname: string, id: string }> | null };

export type ShiftQueryVariables = Exact<{
  shiftId: Scalars['String']['input'];
}>;


export type ShiftQuery = { __typename?: 'Query', shift: { __typename?: 'Shift', id: string, recurringId?: string | null, startDateTime: Date, endDateTime: Date, location?: { __typename?: 'Location', name: string, id: string, emergencyContact?: string | null, address?: string | null } | null, users?: Array<{ __typename?: 'User', fullname: string, id: string }> | null } };

export type ShiftsQueryVariables = Exact<{
  input: ShiftsInput;
}>;


export type ShiftsQuery = { __typename?: 'Query', shifts: Array<{ __typename?: 'Shift', id: string, recurringId?: string | null, startDateTime: Date, endDateTime: Date, location?: { __typename?: 'Location', name: string, id: string, emergencyContact?: string | null, address?: string | null } | null, users?: Array<{ __typename?: 'User', fullname: string, id: string }> | null }> };

export type GetShiftsByUserQueryVariables = Exact<{
  shiftsInput: ShiftsInput;
}>;


export type GetShiftsByUserQuery = { __typename?: 'Query', getUserShifts: Array<{ __typename?: 'Shift', id: string, recurringId?: string | null, startDateTime: Date, endDateTime: Date, location?: { __typename?: 'Location', name: string, id: string, emergencyContact?: string | null, address?: string | null } | null, users?: Array<{ __typename?: 'User', fullname: string, id: string }> | null }> };

export type DeleteUserDocumentMutationVariables = Exact<{
  documentId: Scalars['String']['input'];
}>;


export type DeleteUserDocumentMutation = { __typename?: 'Mutation', removeUserDocument: { __typename?: 'UserDocument', id: string, documentName: string } };

export type UserProfileQueryVariables = Exact<{
  userId: Scalars['String']['input'];
}>;


export type UserProfileQuery = { __typename?: 'Query', userProfile: { __typename?: 'UserProfile', _id: string, permitExpiresAt?: Date | null, gender?: string | null, dob?: Date | null, placeOfBirth?: string | null, currentAddress?: string | null, joinedAt?: Date | null, maritalStatus?: string | null, bankAccNumber?: string | null, bankName?: string | null, id: string, ID?: string | null, ic?: string | null, passport?: string | null, passportExpiresAt?: Date | null, permitNumber?: string | null, emergencyContact?: Array<{ __typename?: 'EmergencyContact', name: string, relation: string, contact: { __typename?: 'Contact', countryCode: string, phone: string } }> | null } };

export type UpdateUserProfileMutationVariables = Exact<{
  userId: Scalars['String']['input'];
  updateUserProfileInput: UpdateUserProfileInput;
}>;


export type UpdateUserProfileMutation = { __typename?: 'Mutation', updateUserProfile: { __typename?: 'UserProfile', id: string } };

export type IndexFaceMutationVariables = Exact<{
  indexFaceInput: IndexFaceInput;
}>;


export type IndexFaceMutation = { __typename?: 'Mutation', indexFace: { __typename?: 'User', id: string, fullname: string, userStatus: UserStatus, role: UserRoles } };

export type ClearFaceMutationVariables = Exact<{
  input: ClearFaceInput;
}>;


export type ClearFaceMutation = { __typename?: 'Mutation', clearFace: boolean };


export const ShiftFragmentFragmentDoc = `
    fragment ShiftFragment on Shift {
  id
  recurringId
  startDateTime
  endDateTime
  location {
    name
    id
    emergencyContact
    address
  }
  users {
    fullname
    id
  }
}
    `;
export const AnnouncementsDocument = `
    query Announcements {
  anouncements {
    id
    title
    description
    date
    userRoles
    document
    createdBy {
      id
      fullname
      role
      profilePicture
    }
  }
}
    `;

export const useAnnouncementsQuery = <
      TData = AnnouncementsQuery,
      TError = Error
    >(
      variables?: AnnouncementsQueryVariables,
      options?: UseQueryOptions<AnnouncementsQuery, TError, TData>
    ) => {
    
    return useQuery<AnnouncementsQuery, TError, TData>(
      variables === undefined ? ['Announcements'] : ['Announcements', variables],
      fetchData<AnnouncementsQuery, AnnouncementsQueryVariables>(AnnouncementsDocument, variables),
      options
    )};

useAnnouncementsQuery.document = AnnouncementsDocument;

useAnnouncementsQuery.getKey = (variables?: AnnouncementsQueryVariables) => variables === undefined ? ['Announcements'] : ['Announcements', variables];

export const AnnouncementDetailDocument = `
    query AnnouncementDetail($id: String!) {
  anouncement(id: $id) {
    title
    description
    document
    createdAt
    document
    _id
    users {
      id
      createdAt
      updatedAt
      fullname
      phone
      userStatus
      role
    }
    createdBy {
      id
      fullname
      role
      profilePicture
    }
  }
}
    `;

export const useAnnouncementDetailQuery = <
      TData = AnnouncementDetailQuery,
      TError = Error
    >(
      variables: AnnouncementDetailQueryVariables,
      options?: UseQueryOptions<AnnouncementDetailQuery, TError, TData>
    ) => {
    
    return useQuery<AnnouncementDetailQuery, TError, TData>(
      ['AnnouncementDetail', variables],
      fetchData<AnnouncementDetailQuery, AnnouncementDetailQueryVariables>(AnnouncementDetailDocument, variables),
      options
    )};

useAnnouncementDetailQuery.document = AnnouncementDetailDocument;

useAnnouncementDetailQuery.getKey = (variables: AnnouncementDetailQueryVariables) => ['AnnouncementDetail', variables];

export const AttendanceDocument = `
    query Attendance($attendanceInput: AttendanceInput!) {
  attendance(attendanceInput: $attendanceInput) {
    id
    createdAt
    updatedAt
    date
    endTime
    location {
      id
      name
    }
    overTime
    overTimeSpentInMinutes
    startTime
    timeSpentInMinutes
  }
}
    `;

export const useAttendanceQuery = <
      TData = AttendanceQuery,
      TError = Error
    >(
      variables: AttendanceQueryVariables,
      options?: UseQueryOptions<AttendanceQuery, TError, TData>
    ) => {
    
    return useQuery<AttendanceQuery, TError, TData>(
      ['Attendance', variables],
      fetchData<AttendanceQuery, AttendanceQueryVariables>(AttendanceDocument, variables),
      options
    )};

useAttendanceQuery.document = AttendanceDocument;

useAttendanceQuery.getKey = (variables: AttendanceQueryVariables) => ['Attendance', variables];

export const ClockInDocument = `
    mutation ClockIn($clockInInput: ClockInInput!) {
  clockIn(clockInInput: $clockInInput) {
    id
  }
}
    `;

export const useClockInMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<ClockInMutation, TError, ClockInMutationVariables, TContext>) => {
    
    return useMutation<ClockInMutation, TError, ClockInMutationVariables, TContext>(
      ['ClockIn'],
      (variables?: ClockInMutationVariables) => fetchData<ClockInMutation, ClockInMutationVariables>(ClockInDocument, variables)(),
      options
    )};

export const ClockOutDocument = `
    mutation ClockOut($clockOutInput: ClockOutInput!) {
  clockOut(clockOutInput: $clockOutInput) {
    id
  }
}
    `;

export const useClockOutMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<ClockOutMutation, TError, ClockOutMutationVariables, TContext>) => {
    
    return useMutation<ClockOutMutation, TError, ClockOutMutationVariables, TContext>(
      ['ClockOut'],
      (variables?: ClockOutMutationVariables) => fetchData<ClockOutMutation, ClockOutMutationVariables>(ClockOutDocument, variables)(),
      options
    )};

export const GetUserAttendanceDocument = `
    query GetUserAttendance($filter: AttendanceFilterInput) {
  myAttendances(filter: $filter) {
    id
    createdAt
    updatedAt
    date
    endTime
    shift {
      startDateTime
      endDateTime
    }
    location {
      id
      name
    }
    overTime
    overTimeSpentInMinutes
    startTime
    timeSpentInMinutes
    actualClockInTime
    actualClockOutTime
  }
}
    `;

export const useGetUserAttendanceQuery = <
      TData = GetUserAttendanceQuery,
      TError = Error
    >(
      variables?: GetUserAttendanceQueryVariables,
      options?: UseQueryOptions<GetUserAttendanceQuery, TError, TData>
    ) => {
    
    return useQuery<GetUserAttendanceQuery, TError, TData>(
      variables === undefined ? ['GetUserAttendance'] : ['GetUserAttendance', variables],
      fetchData<GetUserAttendanceQuery, GetUserAttendanceQueryVariables>(GetUserAttendanceDocument, variables),
      options
    )};

useGetUserAttendanceQuery.document = GetUserAttendanceDocument;

useGetUserAttendanceQuery.getKey = (variables?: GetUserAttendanceQueryVariables) => variables === undefined ? ['GetUserAttendance'] : ['GetUserAttendance', variables];

export const SigninDocument = `
    mutation Signin($input: SignInInput!) {
  signIn(input: $input) {
    access_token
  }
}
    `;

export const useSigninMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<SigninMutation, TError, SigninMutationVariables, TContext>) => {
    
    return useMutation<SigninMutation, TError, SigninMutationVariables, TContext>(
      ['Signin'],
      (variables?: SigninMutationVariables) => fetchData<SigninMutation, SigninMutationVariables>(SigninDocument, variables)(),
      options
    )};

export const FaceLoginDocument = `
    mutation FaceLogin($base64Img: String!) {
  scanFace(input: {base64Img: $base64Img}) {
    access_token
  }
}
    `;

export const useFaceLoginMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<FaceLoginMutation, TError, FaceLoginMutationVariables, TContext>) => {
    
    return useMutation<FaceLoginMutation, TError, FaceLoginMutationVariables, TContext>(
      ['FaceLogin'],
      (variables?: FaceLoginMutationVariables) => fetchData<FaceLoginMutation, FaceLoginMutationVariables>(FaceLoginDocument, variables)(),
      options
    )};

export const ClaimsDocument = `
    query Claims($input: FindClaimsInput) {
  claims(filter: $input) {
    _id
    id
    claimType
    status
    createdAt
    updatedAt
    rejectedReason
    user {
      _id
      fullname
      employeeId
    }
    processedBy {
      _id
      fullname
      employeeId
    }
    claimData {
      ... on AllowanceClaim {
        amount
        purpose
        from
        to
        workingHours
        receipts
      }
      ... on TravelClaim {
        amount
        purpose
        from
        to
        client
        toll
        distance
        receipts
      }
      ... on ExpenseClaim {
        amount
        purpose
        items
        date
        receipts
      }
      ... on SiteClaim {
        amount
        purpose
        items
        receipts
        site {
          name
        }
      }
    }
  }
}
    `;

export const useClaimsQuery = <
      TData = ClaimsQuery,
      TError = Error
    >(
      variables?: ClaimsQueryVariables,
      options?: UseQueryOptions<ClaimsQuery, TError, TData>
    ) => {
    
    return useQuery<ClaimsQuery, TError, TData>(
      variables === undefined ? ['Claims'] : ['Claims', variables],
      fetchData<ClaimsQuery, ClaimsQueryVariables>(ClaimsDocument, variables),
      options
    )};

useClaimsQuery.document = ClaimsDocument;

useClaimsQuery.getKey = (variables?: ClaimsQueryVariables) => variables === undefined ? ['Claims'] : ['Claims', variables];

export const ClaimDocument = `
    query Claim($input: String!) {
  claim(id: $input) {
    _id
    id
    createdAt
    updatedAt
    status
    claimType
    claimData {
      ... on AllowanceClaim {
        amount
        purpose
        from
        to
        workingHours
        receipts
      }
      ... on TravelClaim {
        amount
        purpose
        from
        to
        client
        toll
        distance
        receipts
      }
      ... on ExpenseClaim {
        amount
        purpose
        items
        date
        receipts
      }
      ... on SiteClaim {
        amount
        purpose
        items
        receipts
      }
    }
    rejectedReason
    user {
      _id
      id
      createdAt
      updatedAt
      fullname
      phone
      userStatus
      role
      employeeId
      profilePicture
    }
    processedBy {
      _id
      id
      createdAt
      updatedAt
      fullname
      phone
      userStatus
      role
      employeeId
      profilePicture
    }
  }
}
    `;

export const useClaimQuery = <
      TData = ClaimQuery,
      TError = Error
    >(
      variables: ClaimQueryVariables,
      options?: UseQueryOptions<ClaimQuery, TError, TData>
    ) => {
    
    return useQuery<ClaimQuery, TError, TData>(
      ['Claim', variables],
      fetchData<ClaimQuery, ClaimQueryVariables>(ClaimDocument, variables),
      options
    )};

useClaimQuery.document = ClaimDocument;

useClaimQuery.getKey = (variables: ClaimQueryVariables) => ['Claim', variables];

export const CreateClaimDocument = `
    mutation CreateClaim($input: ClaimInput!) {
  createClaim(input: $input) {
    _id
    id
    createdAt
    updatedAt
    status
    claimType
    rejectedReason
  }
}
    `;

export const useCreateClaimMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<CreateClaimMutation, TError, CreateClaimMutationVariables, TContext>) => {
    
    return useMutation<CreateClaimMutation, TError, CreateClaimMutationVariables, TContext>(
      ['CreateClaim'],
      (variables?: CreateClaimMutationVariables) => fetchData<CreateClaimMutation, CreateClaimMutationVariables>(CreateClaimDocument, variables)(),
      options
    )};

export const UpdateClaimDocument = `
    mutation UpdateClaim($id: String!, $input: UpdateClaimInput!) {
  updateClaim(id: $id, updateClaimInput: $input) {
    _id
  }
}
    `;

export const useUpdateClaimMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<UpdateClaimMutation, TError, UpdateClaimMutationVariables, TContext>) => {
    
    return useMutation<UpdateClaimMutation, TError, UpdateClaimMutationVariables, TContext>(
      ['UpdateClaim'],
      (variables?: UpdateClaimMutationVariables) => fetchData<UpdateClaimMutation, UpdateClaimMutationVariables>(UpdateClaimDocument, variables)(),
      options
    )};

export const CreateSignedUploadUrlDocument = `
    mutation CreateSignedUploadUrl($input: SignedUploadUrlInput!) {
  createSignedUploadUrl(input: $input) {
    url
    fields {
      key
      bucket
      acl
      algorithm
      credential
      date
      Policy
      signature
    }
  }
}
    `;

export const useCreateSignedUploadUrlMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<CreateSignedUploadUrlMutation, TError, CreateSignedUploadUrlMutationVariables, TContext>) => {
    
    return useMutation<CreateSignedUploadUrlMutation, TError, CreateSignedUploadUrlMutationVariables, TContext>(
      ['CreateSignedUploadUrl'],
      (variables?: CreateSignedUploadUrlMutationVariables) => fetchData<CreateSignedUploadUrlMutation, CreateSignedUploadUrlMutationVariables>(CreateSignedUploadUrlDocument, variables)(),
      options
    )};

export const CreateUserDocumentDocument = `
    mutation CreateUserDocument($input: CreateUserDocumentInput!) {
  createUserDocument(createUserDocumentInput: $input) {
    id
  }
}
    `;

export const useCreateUserDocumentMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<CreateUserDocumentMutation, TError, CreateUserDocumentMutationVariables, TContext>) => {
    
    return useMutation<CreateUserDocumentMutation, TError, CreateUserDocumentMutationVariables, TContext>(
      ['CreateUserDocument'],
      (variables?: CreateUserDocumentMutationVariables) => fetchData<CreateUserDocumentMutation, CreateUserDocumentMutationVariables>(CreateUserDocumentDocument, variables)(),
      options
    )};

export const UserDocumentsDocument = `
    query UserDocuments($userDocumentFilterInput: UserDocumentFilterInput!) {
  userDocuments(filter: $userDocumentFilterInput) {
    id
    documentName
    url
    createdAt
  }
}
    `;

export const useUserDocumentsQuery = <
      TData = UserDocumentsQuery,
      TError = Error
    >(
      variables: UserDocumentsQueryVariables,
      options?: UseQueryOptions<UserDocumentsQuery, TError, TData>
    ) => {
    
    return useQuery<UserDocumentsQuery, TError, TData>(
      ['UserDocuments', variables],
      fetchData<UserDocumentsQuery, UserDocumentsQueryVariables>(UserDocumentsDocument, variables),
      options
    )};

useUserDocumentsQuery.document = UserDocumentsDocument;

useUserDocumentsQuery.getKey = (variables: UserDocumentsQueryVariables) => ['UserDocuments', variables];

export const CreateIncidentDocument = `
    mutation CreateIncident($input: CreateIncidentInput!) {
  createIncident(createIncidentInput: $input) {
    _id
  }
}
    `;

export const useCreateIncidentMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<CreateIncidentMutation, TError, CreateIncidentMutationVariables, TContext>) => {
    
    return useMutation<CreateIncidentMutation, TError, CreateIncidentMutationVariables, TContext>(
      ['CreateIncident'],
      (variables?: CreateIncidentMutationVariables) => fetchData<CreateIncidentMutation, CreateIncidentMutationVariables>(CreateIncidentDocument, variables)(),
      options
    )};

export const CreateInventoryRequestDocument = `
    mutation CreateInventoryRequest($input: CreateInventoryRequestInput!) {
  createInventoryRequest(input: $input) {
    _id
    id
    createdAt
    updatedAt
    status
    acceptedAt
    inventoryType
    inventory {
      _id
      id
      createdAt
      updatedAt
      item
      description
      type
      attributes {
        attibuteName
        attributeValues
      }
      items {
        item
        quantity
        sku
        costPrice
        sellingPrice
      }
    }
    acceptedBy {
      _id
      id
      createdAt
      updatedAt
      fullname
      phone
      userStatus
      role
      profilePicture
    }
    requestedBy {
      _id
      id
      createdAt
      updatedAt
      fullname
      phone
      userStatus
      role
      profilePicture
    }
  }
}
    `;

export const useCreateInventoryRequestMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<CreateInventoryRequestMutation, TError, CreateInventoryRequestMutationVariables, TContext>) => {
    
    return useMutation<CreateInventoryRequestMutation, TError, CreateInventoryRequestMutationVariables, TContext>(
      ['CreateInventoryRequest'],
      (variables?: CreateInventoryRequestMutationVariables) => fetchData<CreateInventoryRequestMutation, CreateInventoryRequestMutationVariables>(CreateInventoryRequestDocument, variables)(),
      options
    )};

export const GetInventoryDocument = `
    query GetInventory {
  inventory {
    _id
    id
    createdAt
    updatedAt
    item
    description
    type
    items {
      item
      quantity
      sku
      costPrice
      sellingPrice
    }
    attributes {
      attibuteName
      attributeValues
    }
  }
}
    `;

export const useGetInventoryQuery = <
      TData = GetInventoryQuery,
      TError = Error
    >(
      variables?: GetInventoryQueryVariables,
      options?: UseQueryOptions<GetInventoryQuery, TError, TData>
    ) => {
    
    return useQuery<GetInventoryQuery, TError, TData>(
      variables === undefined ? ['GetInventory'] : ['GetInventory', variables],
      fetchData<GetInventoryQuery, GetInventoryQueryVariables>(GetInventoryDocument, variables),
      options
    )};

useGetInventoryQuery.document = GetInventoryDocument;

useGetInventoryQuery.getKey = (variables?: GetInventoryQueryVariables) => variables === undefined ? ['GetInventory'] : ['GetInventory', variables];

export const CreateLeaveDocument = `
    mutation CreateLeave($input: CreateLeaveInput!) {
  createLeave(createLeaveInput: $input) {
    _id
  }
}
    `;

export const useCreateLeaveMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<CreateLeaveMutation, TError, CreateLeaveMutationVariables, TContext>) => {
    
    return useMutation<CreateLeaveMutation, TError, CreateLeaveMutationVariables, TContext>(
      ['CreateLeave'],
      (variables?: CreateLeaveMutationVariables) => fetchData<CreateLeaveMutation, CreateLeaveMutationVariables>(CreateLeaveDocument, variables)(),
      options
    )};

export const GetLeavesDocument = `
    query GetLeaves($input: FindLeavesInput!) {
  leaves(filter: $input) {
    _id
    id
    createdAt
    updatedAt
    reason
    leaveType
    startDateTime
    endDateTime
    leaveStatus
    rejectedReason
    user {
      _id
      fullname
      phone
      userStatus
      role
    }
    approvedBy {
      _id
      fullname
      phone
      userStatus
      role
    }
  }
}
    `;

export const useGetLeavesQuery = <
      TData = GetLeavesQuery,
      TError = Error
    >(
      variables: GetLeavesQueryVariables,
      options?: UseQueryOptions<GetLeavesQuery, TError, TData>
    ) => {
    
    return useQuery<GetLeavesQuery, TError, TData>(
      ['GetLeaves', variables],
      fetchData<GetLeavesQuery, GetLeavesQueryVariables>(GetLeavesDocument, variables),
      options
    )};

useGetLeavesQuery.document = GetLeavesDocument;

useGetLeavesQuery.getKey = (variables: GetLeavesQueryVariables) => ['GetLeaves', variables];

export const LeaveDocument = `
    query Leave($id: String!) {
  leave(id: $id) {
    _id
    id
    createdAt
    updatedAt
    reason
    leaveType
    startDateTime
    endDateTime
    leaveStatus
    rejectedReason
    approvedBy {
      _id
      id
      createdAt
      updatedAt
      fullname
      phone
      userStatus
      role
      employeeId
      profilePicture
    }
    user {
      _id
      id
      createdAt
      updatedAt
      fullname
      phone
      userStatus
      profilePicture
    }
  }
}
    `;

export const useLeaveQuery = <
      TData = LeaveQuery,
      TError = Error
    >(
      variables: LeaveQueryVariables,
      options?: UseQueryOptions<LeaveQuery, TError, TData>
    ) => {
    
    return useQuery<LeaveQuery, TError, TData>(
      ['Leave', variables],
      fetchData<LeaveQuery, LeaveQueryVariables>(LeaveDocument, variables),
      options
    )};

useLeaveQuery.document = LeaveDocument;

useLeaveQuery.getKey = (variables: LeaveQueryVariables) => ['Leave', variables];

export const LocationsDocument = `
    query Locations {
  locations {
    id
    name
    description
    address
    emergencyContact
  }
}
    `;

export const useLocationsQuery = <
      TData = LocationsQuery,
      TError = Error
    >(
      variables?: LocationsQueryVariables,
      options?: UseQueryOptions<LocationsQuery, TError, TData>
    ) => {
    
    return useQuery<LocationsQuery, TError, TData>(
      variables === undefined ? ['Locations'] : ['Locations', variables],
      fetchData<LocationsQuery, LocationsQueryVariables>(LocationsDocument, variables),
      options
    )};

useLocationsQuery.document = LocationsDocument;

useLocationsQuery.getKey = (variables?: LocationsQueryVariables) => variables === undefined ? ['Locations'] : ['Locations', variables];

export const MeDocument = `
    query Me {
  me {
    _id
    id
    createdAt
    updatedAt
    fullname
    phone
    userStatus
    role
    profilePicture
    faceInformation {
      faceId
    }
  }
}
    `;

export const useMeQuery = <
      TData = MeQuery,
      TError = Error
    >(
      variables?: MeQueryVariables,
      options?: UseQueryOptions<MeQuery, TError, TData>
    ) => {
    
    return useQuery<MeQuery, TError, TData>(
      variables === undefined ? ['Me'] : ['Me', variables],
      fetchData<MeQuery, MeQueryVariables>(MeDocument, variables),
      options
    )};

useMeQuery.document = MeDocument;

useMeQuery.getKey = (variables?: MeQueryVariables) => variables === undefined ? ['Me'] : ['Me', variables];

export const ShiftDocument = `
    query Shift($shiftId: String!) {
  shift(shiftId: $shiftId) {
    ...ShiftFragment
  }
}
    ${ShiftFragmentFragmentDoc}`;

export const useShiftQuery = <
      TData = ShiftQuery,
      TError = Error
    >(
      variables: ShiftQueryVariables,
      options?: UseQueryOptions<ShiftQuery, TError, TData>
    ) => {
    
    return useQuery<ShiftQuery, TError, TData>(
      ['Shift', variables],
      fetchData<ShiftQuery, ShiftQueryVariables>(ShiftDocument, variables),
      options
    )};

useShiftQuery.document = ShiftDocument;

useShiftQuery.getKey = (variables: ShiftQueryVariables) => ['Shift', variables];

export const ShiftsDocument = `
    query Shifts($input: ShiftsInput!) {
  shifts(shiftsInput: $input) {
    ...ShiftFragment
  }
}
    ${ShiftFragmentFragmentDoc}`;

export const useShiftsQuery = <
      TData = ShiftsQuery,
      TError = Error
    >(
      variables: ShiftsQueryVariables,
      options?: UseQueryOptions<ShiftsQuery, TError, TData>
    ) => {
    
    return useQuery<ShiftsQuery, TError, TData>(
      ['Shifts', variables],
      fetchData<ShiftsQuery, ShiftsQueryVariables>(ShiftsDocument, variables),
      options
    )};

useShiftsQuery.document = ShiftsDocument;

useShiftsQuery.getKey = (variables: ShiftsQueryVariables) => ['Shifts', variables];

export const GetShiftsByUserDocument = `
    query GetShiftsByUser($shiftsInput: ShiftsInput!) {
  getUserShifts(shiftsInput: $shiftsInput) {
    ...ShiftFragment
  }
}
    ${ShiftFragmentFragmentDoc}`;

export const useGetShiftsByUserQuery = <
      TData = GetShiftsByUserQuery,
      TError = Error
    >(
      variables: GetShiftsByUserQueryVariables,
      options?: UseQueryOptions<GetShiftsByUserQuery, TError, TData>
    ) => {
    
    return useQuery<GetShiftsByUserQuery, TError, TData>(
      ['GetShiftsByUser', variables],
      fetchData<GetShiftsByUserQuery, GetShiftsByUserQueryVariables>(GetShiftsByUserDocument, variables),
      options
    )};

useGetShiftsByUserQuery.document = GetShiftsByUserDocument;

useGetShiftsByUserQuery.getKey = (variables: GetShiftsByUserQueryVariables) => ['GetShiftsByUser', variables];

export const DeleteUserDocumentDocument = `
    mutation DeleteUserDocument($documentId: String!) {
  removeUserDocument(id: $documentId) {
    id
    documentName
  }
}
    `;

export const useDeleteUserDocumentMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<DeleteUserDocumentMutation, TError, DeleteUserDocumentMutationVariables, TContext>) => {
    
    return useMutation<DeleteUserDocumentMutation, TError, DeleteUserDocumentMutationVariables, TContext>(
      ['DeleteUserDocument'],
      (variables?: DeleteUserDocumentMutationVariables) => fetchData<DeleteUserDocumentMutation, DeleteUserDocumentMutationVariables>(DeleteUserDocumentDocument, variables)(),
      options
    )};

export const UserProfileDocument = `
    query UserProfile($userId: String!) {
  userProfile(id: $userId) {
    _id
    permitExpiresAt
    gender
    dob
    placeOfBirth
    currentAddress
    joinedAt
    maritalStatus
    bankAccNumber
    bankName
    emergencyContact {
      name
      relation
      contact {
        countryCode
        phone
      }
    }
    id
    ID
    ic
    passport
    passportExpiresAt
    permitNumber
  }
}
    `;

export const useUserProfileQuery = <
      TData = UserProfileQuery,
      TError = Error
    >(
      variables: UserProfileQueryVariables,
      options?: UseQueryOptions<UserProfileQuery, TError, TData>
    ) => {
    
    return useQuery<UserProfileQuery, TError, TData>(
      ['UserProfile', variables],
      fetchData<UserProfileQuery, UserProfileQueryVariables>(UserProfileDocument, variables),
      options
    )};

useUserProfileQuery.document = UserProfileDocument;

useUserProfileQuery.getKey = (variables: UserProfileQueryVariables) => ['UserProfile', variables];

export const UpdateUserProfileDocument = `
    mutation UpdateUserProfile($userId: String!, $updateUserProfileInput: UpdateUserProfileInput!) {
  updateUserProfile(id: $userId, updateUserProfileInput: $updateUserProfileInput) {
    id
  }
}
    `;

export const useUpdateUserProfileMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<UpdateUserProfileMutation, TError, UpdateUserProfileMutationVariables, TContext>) => {
    
    return useMutation<UpdateUserProfileMutation, TError, UpdateUserProfileMutationVariables, TContext>(
      ['UpdateUserProfile'],
      (variables?: UpdateUserProfileMutationVariables) => fetchData<UpdateUserProfileMutation, UpdateUserProfileMutationVariables>(UpdateUserProfileDocument, variables)(),
      options
    )};

export const IndexFaceDocument = `
    mutation IndexFace($indexFaceInput: IndexFaceInput!) {
  indexFace(indexFaceInput: $indexFaceInput) {
    id
    fullname
    userStatus
    role
  }
}
    `;

export const useIndexFaceMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<IndexFaceMutation, TError, IndexFaceMutationVariables, TContext>) => {
    
    return useMutation<IndexFaceMutation, TError, IndexFaceMutationVariables, TContext>(
      ['IndexFace'],
      (variables?: IndexFaceMutationVariables) => fetchData<IndexFaceMutation, IndexFaceMutationVariables>(IndexFaceDocument, variables)(),
      options
    )};

export const ClearFaceDocument = `
    mutation ClearFace($input: ClearFaceInput!) {
  clearFace(clearFaceInput: $input)
}
    `;

export const useClearFaceMutation = <
      TError = Error,
      TContext = unknown
    >(options?: UseMutationOptions<ClearFaceMutation, TError, ClearFaceMutationVariables, TContext>) => {
    
    return useMutation<ClearFaceMutation, TError, ClearFaceMutationVariables, TContext>(
      ['ClearFace'],
      (variables?: ClearFaceMutationVariables) => fetchData<ClearFaceMutation, ClearFaceMutationVariables>(ClearFaceDocument, variables)(),
      options
    )};
